  def adsTestItemSite(items: List[com.guwave.onedata.dataware.dw.common.ads.model.value.TestItemDetail], isPassOnly: Int, productList: Broadcast[List[OdsProductConfig]]): TestItemSite = {
    val item = items.head
    val testitemType = item.TESTITEM_TYPE
    val tetsValues = items.filter(t => t.TEST_VALUE != null && t.TEST_RESULT != null && t.TEST_RESULT < 2).map(_.TEST_VALUE.doubleValue())
    val values = tetsValues.toArray

    val (product, productType, productFamily) = if (productList.value.nonEmpty) {
      val productInfo = productList.value.head
      (if (productInfo.PRODUCT == null) EMPTY else productInfo.PRODUCT, if (productInfo.PRODUCT_TYPE == null) EMPTY else productInfo.PRODUCT_TYPE, if (productInfo.PRODUCT_FAMILY == null) EMPTY else productInfo.PRODUCT_FAMILY)
    } else {
      (EMPTY, EMPTY, EMPTY)
    }
    val now = System.currentTimeMillis()
    val createHourKey = DateUtil.getDayHour(now)
    val createDayKey = DateUtil.getDay(now)

    // 去重组合为String
    val sblotidarr = DwsCommonUtil.mkStringDistinct(items.map(_.SBLOT_ID))
    val wafernoarr = DwsCommonUtil.mkStringDistinct(items.map(_.WAFER_NO))
    val waferidarr = DwsCommonUtil.mkStringDistinct(items.map(_.WAFER_ID))
    val waferlotidarr = DwsCommonUtil.mkStringDistinct(items.map(_.WAFER_LOT_ID))
    val unitsarr = DwsCommonUtil.mkStringDistinct(items.map(_.UNITS))
    val originunitsarr = DwsCommonUtil.mkStringDistinct(items.map(_.ORIGIN_UNITS))
    val lolimitarr = DwsCommonUtil.mkStringDistinct(items.filter(_.LO_LIMIT != null).map(_.LO_LIMIT.toString()))
    val hilimitarr = DwsCommonUtil.mkStringDistinct(items.filter(_.HI_LIMIT != null).map(_.HI_LIMIT.toString()))
    val originlolimitarr = DwsCommonUtil.mkStringDistinct(items.filter(_.ORIGIN_LO_LIMIT != null).map(_.ORIGIN_LO_LIMIT.toString()))
    val originhilimitarr = DwsCommonUtil.mkStringDistinct(items.filter(_.ORIGIN_HI_LIMIT != null).map(_.ORIGIN_HI_LIMIT.toString()))
    val processarr = DwsCommonUtil.mkStringDistinct(items.map(_.PROCESS))
    val testitemtypearr = DwsCommonUtil.mkStringDistinct(items.map(_.TESTITEM_TYPE))
    val testtemperaturearr = DwsCommonUtil.mkStringDistinct(items.map(_.TEST_TEMPERATURE))
    val testernamearr = DwsCommonUtil.mkStringDistinct(items.map(_.TESTER_NAME))
    val testertypearr = DwsCommonUtil.mkStringDistinct(items.map(_.TESTER_TYPE))
    val proberhandleridarr = DwsCommonUtil.mkStringDistinct(items.map(_.PROBER_HANDLER_ID))
    val probecardloadboardidarr = DwsCommonUtil.mkStringDistinct(items.map(_.PROBECARD_LOADBOARD_ID))
    val proberhandlertyparr = DwsCommonUtil.mkStringDistinct(items.map(_.PROBER_HANDLER_TYP))
    val probecardloadboardtyparr = DwsCommonUtil.mkStringDistinct(items.map(_.PROBECARD_LOADBOARD_TYP))
    val sitecntarr = DwsCommonUtil.mkStringDistinct(items.filter(_.SITE_CNT != null).map(_.SITE_CNT.toString()))
    val sitenumsarr = DwsCommonUtil.mkStringDistinct(items.map(_.SITE_NUMS))

    val sblotid = if (SUPPORT_CP_TEST_AREA_LIST.contains(item.TEST_AREA)) sblotidarr else item.SBLOT_ID
    val waferNo = if (SUPPORT_CP_TEST_AREA_LIST.contains(item.TEST_AREA)) item.WAFER_NO else wafernoarr

    val inputCnt = items.filter(_.IS_STANDARD_ECID == 1).map(_.ECID).distinct.size + items.filter(_.IS_STANDARD_ECID != 1).map(_.ECID).distinct.size
    val passCnt = items.filter(_.IS_STANDARD_ECID == 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).map(_.ECID).distinct.size + items.filter(_.IS_STANDARD_ECID != 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).map(_.ECID).distinct.size
    val failCnt = items.filter(_.IS_STANDARD_ECID == 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT != 1).map(_.ECID).distinct.size + items.filter(_.IS_STANDARD_ECID != 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT != 1).map(_.ECID).distinct.size
    val passBinFailIngItemCnt = items.filter(_.IS_STANDARD_ECID == 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).filter(_.HBIN_PF.equals(PF_PASS)).map(_.ECID).distinct.size + items.filter(_.IS_STANDARD_ECID != 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).filter(_.HBIN_PF.equals(PF_PASS)).map(_.ECID).distinct.size
    val exeInputCnt = items.filter(_.IS_STANDARD_ECID == 1).map(_.TEST_VALUE).size + items.filter(_.IS_STANDARD_ECID != 1).map(_.TEST_VALUE).size
    val exePassCnt = items.filter(_.IS_STANDARD_ECID == 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).map(_.TEST_VALUE).size + items.filter(_.IS_STANDARD_ECID != 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT == 1).map(_.TEST_VALUE).size
    val exeFailCnt = items.filter(_.IS_STANDARD_ECID == 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT != 1).map(_.TEST_VALUE).size + items.filter(_.IS_STANDARD_ECID != 1).filter(t => t.TEST_RESULT != null && t.TEST_RESULT != 1).map(_.TEST_VALUE).size

    val startTimeMin = items.map(_.START_TIME).filter(_ != null).toArray.min
    val startHourMin = items.map(_.START_HOUR_KEY).filter(_ != null).toArray.min
    val startDayMin = items.map(_.START_DAY_KEY).filter(_ != null).toArray.min
    val endTimeMax = items.map(_.END_TIME).filter(_ != null).toArray.max
    val endHourMax = items.map(_.END_HOUR_KEY).filter(_ != null).toArray.max
    val endDayMax = items.map(_.END_DAY_KEY).filter(_ != null).toArray.max

    if (((P.equals(testitemType) || M.equals(testitemType)) && (null != values && !values.isEmpty))) {

      // 从items里取START_TIME = starTimeMax的任意一条,取HI_LIMIT和LO_LIMIT
      val starTimeMax = items.map(_.START_TIME).filter(_ != null).toArray.max
      val latestItem = items.filter(_.START_TIME == starTimeMax).head
      val hiLatest = latestItem.HI_LIMIT
      val loLatest = latestItem.LO_LIMIT
      val himax = if (hiLatest != null) hiLatest.doubleValue() else Double.NaN
      val lomin = if (loLatest != null) loLatest.doubleValue() else Double.NaN

      val median = MathUtil.quantile50(values)
      val mean = MathUtil.mean(values).doubleValue()
      val max = CommonUtil.safeDecimal(values.max)
      val min = CommonUtil.safeDecimal(values.min)
      val squareSum = MathUtil.squareSum(values)
      val sum = CommonUtil.safeDecimal(MathUtil.sum(values).doubleValue())
      val stdDevP = MathUtil.stdDev(values)
      val stdDevS = CommonUtil.safeDecimal(MathUtil.divide(squareSum.doubleValue(), MathUtil.minus(exeInputCnt, 1)))
      val range = max.subtract(min).doubleValue()
      val q1 = MathUtil.quantile25(values)
      val q3 = MathUtil.quantile75(values)
      val p1 = CommonUtil.safeDecimal(MathUtil.quantile1(values))
      val p5 = CommonUtil.safeDecimal(MathUtil.quantile5(values))
      val p10 = CommonUtil.safeDecimal(MathUtil.quantile10(values))
      val p90 = CommonUtil.safeDecimal(MathUtil.quantile90(values))
      val p95 = CommonUtil.safeDecimal(MathUtil.quantile95(values))
      val p99 = CommonUtil.safeDecimal(MathUtil.quantile99(values))
      val iqr = MathUtil.minus(q3, q1)
      val cp = if (loLatest != null && hiLatest != null) CommonUtil.safeDecimal(MathUtil.cp(stdDevP, lomin, himax)) else null
      val cpk = if (loLatest != null && hiLatest != null) CommonUtil.safeDecimal(MathUtil.cpk(mean, stdDevP, lomin, himax)) else null
      val cpu = if (hiLatest != null) CommonUtil.safeDecimal(MathUtil.cpu(mean, stdDevP, himax)) else null
      val cpl = if (loLatest != null) CommonUtil.safeDecimal(MathUtil.cpl(mean, stdDevP, lomin)) else null
      val ca = if (himax.isNaN || lomin.isNaN || himax.equals(lomin)) {
        null
      } else {
        CommonUtil.safeDecimal(MathUtil.divide(
          MathUtil.minus(2 * mean, MathUtil.add(himax, lomin)),
          MathUtil.minus(himax, lomin)))
      }

      val skewness = MathUtil.skewness(values)

      val kurtosis = MathUtil.kurtosis(values)

      //聚合后过滤计算字段
      val lowerfilter = MathUtil.minus(2.5 * q1, 1.5 * q3)
      val upperfilter = MathUtil.minus(2.5 * q3, 1.5 * q1)
      val maxwo = if (!values.exists(_ < upperfilter)) null else CommonUtil.safeDecimal(values.filter(_ < upperfilter).max)
      val minwo = if (!values.exists(_ > lowerfilter)) null else CommonUtil.safeDecimal(values.filter(_ > lowerfilter).min)
      val outliercnt = values.count(a => a < lowerfilter || a > upperfilter)

      val norparameter = if (!himax.isNaN && !lomin.isNaN && himax + lomin == 0) himax else if (himax.isNaN || lomin.isNaN) Double.NaN else (himax + lomin) / 2
      val norflag = if (himax.isNaN || lomin.isNaN || norparameter.isNaN || norparameter == 0) true else false

      //归一化
      val normedian = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(median, norparameter), norparameter))
      val normean = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(mean, norparameter), norparameter))
      val normax = if (norflag || max == null) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(max.doubleValue(), norparameter), norparameter))
      val normin = if (norflag || min == null) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(min.doubleValue(), norparameter), norparameter))
      val normaxwooutliers = if (norflag || maxwo == null) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(maxwo.doubleValue(), norparameter), norparameter))
      val norminwooutliers = if (norflag || minwo == null) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(minwo.doubleValue(), norparameter), norparameter))
      val noriqr = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(iqr, norparameter), norparameter))
      val norq1 = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(q1, norparameter), norparameter))
      val norq3 = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(q3, norparameter), norparameter))
      val norlower = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(lowerfilter.doubleValue(), norparameter), norparameter))
      val norupper = if (norflag) null else CommonUtil.safeDecimal(MathUtil.divide(MathUtil.minus(upperfilter.doubleValue(), norparameter), norparameter))

      val lower = if (maxwo == null) CommonUtil.safeDecimal(lowerfilter) else if (lowerfilter >= maxwo.doubleValue()) CommonUtil.safeDecimal(lowerfilter) else maxwo
      val upper = if (minwo == null) CommonUtil.safeDecimal(upperfilter) else if (upperfilter <= minwo.doubleValue()) CommonUtil.safeDecimal(upperfilter) else minwo

      val idxArr = if (range == 0) Array(ZERO) else values.map(a => {
        val res = floor(MathUtil.minus(a, min.doubleValue()) / MathUtil.divide(range, 100))
        if (res > 99 || res < 0) {
          ZERO
        }
        res.toInt.toString
      })

      val groupdetail: Map[String, java.lang.Long] = idxArr.groupBy(identity).mapValues(_.length)


      TestItemSite(if (TestArea.getCpMapDataSourceList.contains(TestArea.of(item.TEST_AREA))) CP_MAP else if (TestArea.getCpInklessMapDataDourceList.contains(TestArea.of(item.TEST_AREA))) INKLESS_MAP else TEST_RAW_DATA,
        item.UPLOAD_TYPE, item.CUSTOMER, item.SUB_CUSTOMER, item.FAB, item.FAB_SITE, item.FACTORY, item.FACTORY_SITE, item.TEST_AREA, item.TEST_STAGE,
        item.DEVICE_ID, item.LOT_TYPE, item.LOT_ID, sblotid, waferidarr, waferNo, waferlotidarr, product, productType, productFamily, item.TEST_PROGRAM,
        item.TEST_PROGRAM_VERSION, item.TEST_NUM, item.TEST_TXT, item.TEST_ITEM, item.SITE, isPassOnly, if (item.IS_FINAL_TEST_IGNORE_TP == null) 1 else item.IS_FINAL_TEST_IGNORE_TP,
        unitsarr, originunitsarr, lolimitarr, hilimitarr, originlolimitarr, originhilimitarr, processarr, testitemtypearr, testtemperaturearr, testernamearr,
        testertypearr, proberhandlertyparr, proberhandleridarr, probecardloadboardtyparr, probecardloadboardidarr, sitecntarr, sitenumsarr, inputCnt, passCnt,
        failCnt, passBinFailIngItemCnt, exeInputCnt, exePassCnt, exeFailCnt, CommonUtil.safeDecimal(median), CommonUtil.safeDecimal(mean), max, min, maxwo,
        minwo, squareSum, sum, CommonUtil.safeDecimal(stdDevP), stdDevS, CommonUtil.safeDecimal(range), CommonUtil.safeDecimal(iqr), CommonUtil.safeDecimal(q1),
        CommonUtil.safeDecimal(q3), lower, upper, outliercnt, p1, p5, p10, p90, p95, p99, groupdetail, cp, cpu, cpl, cpk, cp, cpu, cpl, cpk, ca, skewness,
        kurtosis, normedian, normean, normax, normin, normaxwooutliers, norminwooutliers, noriqr, norq1, norq3, norlower, norupper, startTimeMin, startHourMin,
        startDayMin, endTimeMax, endHourMax, endDayMax, now, createHourKey, createDayKey, SYSTEM, if (item.UPLOAD_TIME == null) item.CREATE_TIME else item.UPLOAD_TIME, item.VERSION, 0)
    } else {
      TestItemSite(if (TestArea.getCpMapDataSourceList.contains(TestArea.of(item.TEST_AREA))) CP_MAP else if (TestArea.getCpInklessMapDataDourceList.contains(TestArea.of(item.TEST_AREA))) INKLESS_MAP else TEST_RAW_DATA,
        item.UPLOAD_TYPE, item.CUSTOMER, item.SUB_CUSTOMER, item.FAB, item.FAB_SITE, item.FACTORY, item.FACTORY_SITE, item.TEST_AREA, item.TEST_STAGE,
        item.DEVICE_ID, item.LOT_TYPE, item.LOT_ID, sblotid, waferidarr, waferNo, waferlotidarr, product, productType, productFamily, item.TEST_PROGRAM,
        item.TEST_PROGRAM_VERSION, item.TEST_NUM, item.TEST_TXT, item.TEST_ITEM, item.SITE, isPassOnly, if (item.IS_FINAL_TEST_IGNORE_TP == null) 1 else item.IS_FINAL_TEST_IGNORE_TP,
        unitsarr, originunitsarr, lolimitarr, hilimitarr, originlolimitarr, originhilimitarr, processarr, testitemtypearr, testtemperaturearr,
        testernamearr, testertypearr, proberhandlertyparr, proberhandleridarr, probecardloadboardtyparr, probecardloadboardidarr, sitecntarr,
        sitenumsarr, inputCnt, passCnt, failCnt, passBinFailIngItemCnt, exeInputCnt, exePassCnt, exeFailCnt, null, null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null, 0L, null, null, null, null, null, null, Map(), null, null, null, null, null,
        null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, startTimeMin, startHourMin, startDayMin,
        endTimeMax, endHourMax, endDayMax, now, createHourKey, createDayKey, SYSTEM, if (item.UPLOAD_TIME == null) item.CREATE_TIME else item.UPLOAD_TIME, item.VERSION, 0)
    }
  }